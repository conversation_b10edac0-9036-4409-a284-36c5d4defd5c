//
//  AlgorithmTests.swift
//  pvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/4.
//

import Foundation
import simd

// 简单的算法验证测试
class AlgorithmTests {
    
    static func runBasicTests() {
        print("🧪 开始算法基础测试...")
        
        testPlaneEquation()
        testSlopeAnalyzer()
        testPhysicsSimulator()
        testTrajectoryOptimizer()
        
        print("✅ 所有基础测试完成")
    }
    
    static func testPlaneEquation() {
        print("📐 测试平面方程...")
        
        let plane = PlaneEquation(
            a: 0.1, b: 0.05, c: 0.0,
            normal: simd_normalize(simd_float3(-0.1, 1, -0.05)),
            slopeAngle: atan(sqrt(0.1*0.1 + 0.05*0.05)),
            downhillDirection: simd_normalize(simd_float3(-0.1, 0, -0.05))
        )
        
        // 验证平面方程
        let testPoint = simd_float3(1.0, 0.0, 2.0)
        let expectedY = plane.a * testPoint.x + plane.b * testPoint.z + plane.c
        print("  平面方程 y = \(plane.a)x + \(plane.b)z + \(plane.c)")
        print("  测试点 (1,0,2) 期望y: \(expectedY)")
        
        assert(abs(plane.normal.y) > 0.9, "法向量应该朝上")
        print("  ✓ 平面方程测试通过")
    }
    
    static func testSlopeAnalyzer() {
        print("📊 测试坡度分析...")
        
        let analyzer = SlopeAnalyzer()
        
        // 创建一个简单的倾斜平面点云
        var points: [simd_float3] = []
        for x in stride(from: -1.0, through: 1.0, by: 0.2) {
            for z in stride(from: -1.0, through: 1.0, by: 0.2) {
                let y = 0.1 * x + 0.05 * z // 简单的倾斜平面
                points.append(simd_float3(Float(x), Float(y), Float(z)))
            }
        }
        
        guard let plane = analyzer.fitPlane(points: points) else {
            fatalError("平面拟合失败")
        }
        
        print("  拟合结果: a=\(plane.a), b=\(plane.b), c=\(plane.c)")
        print("  坡度角: \(plane.slopeAngle * 180 / .pi)°")
        
        // 验证拟合精度
        assert(abs(plane.a - 0.1) < 0.01, "a系数拟合不准确")
        assert(abs(plane.b - 0.05) < 0.01, "b系数拟合不准确")
        print("  ✓ 坡度分析测试通过")
    }
    
    static func testPhysicsSimulator() {
        print("⚽ 测试物理模拟...")
        
        let simulator = PhysicsSimulator()
        
        // 创建一个水平平面
        let plane = PlaneEquation(
            a: 0, b: 0, c: 0,
            normal: simd_float3(0, 1, 0),
            slopeAngle: 0,
            downhillDirection: simd_float3(1, 0, 0)
        )
        
        let trajectory = simulator.simulate(
            initialPosition: simd_float3(0, 0, 0),
            launchAngle: 0,
            launchSpeed: 1.0,
            plane: plane
        )
        
        print("  轨迹点数: \(trajectory.count)")
        print("  起始点: \(trajectory.first!)")
        print("  结束点: \(trajectory.last!)")
        
        assert(trajectory.count > 10, "轨迹点数太少")
        assert(trajectory.first!.x == 0, "起始点不正确")
        print("  ✓ 物理模拟测试通过")
    }
    
    static func testTrajectoryOptimizer() {
        print("🎯 测试轨迹优化...")
        
        let optimizer = TrajectoryOptimizer()
        
        let ball = simd_float3(0, 0, 0)
        let hole = simd_float3(3, 0, 0) // 3米直线距离
        
        let plane = PlaneEquation(
            a: 0, b: 0, c: 0,
            normal: simd_float3(0, 1, 0),
            slopeAngle: 0,
            downhillDirection: simd_float3(1, 0, 0)
        )
        
        let speed = optimizer.estimateBaseSpeed(distance: 3.0)
        let angle = optimizer.findOptimalAngle(ball: ball, hole: hole, plane: plane)
        
        print("  估算速度: \(speed) m/s")
        print("  最优角度: \(angle * 180 / .pi)°")
        
        assert(speed > 0, "估算速度应该大于0")
        assert(abs(angle) < .pi/6, "最优角度应该在合理范围内")
        print("  ✓ 轨迹优化测试通过")
    }
}
