//
//  QuickTest.swift
//  pvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/4.
//

import Foundation
import simd

// 快速验证核心算法的独立测试
func runQuickTests() {
    print("🚀 开始快速验证测试...")
    
    // 测试1: 数学扩展
    let vec4 = simd_float4(1, 2, 3, 1)
    let vec3 = vec4.xyz
    assert(vec3.x == 1 && vec3.y == 2 && vec3.z == 3, "simd扩展失败")
    
    let dist = distanceXZ(simd_float3(0, 0, 0), simd_float3(3, 5, 4))
    assert(abs(dist - 5.0) < 0.001, "XZ距离计算错误")
    print("✓ 数学扩展测试通过")
    
    // 测试2: 物理常数
    assert(PhysicsConstants.gravity == 9.81, "重力常数错误")
    assert(PhysicsConstants.holeRadius > 0, "洞半径应该大于0")
    print("✓ 物理常数测试通过")
    
    // 测试3: 平面方程
    let plane = PlaneEquation(
        a: 0.1, b: 0.05, c: 0.0,
        normal: simd_normalize(simd_float3(-0.1, 1, -0.05)),
        slopeAngle: atan(sqrt(0.1*0.1 + 0.05*0.05)),
        downhillDirection: simd_normalize(simd_float3(-0.1, 0, -0.05))
    )
    assert(plane.normal.y > 0.9, "法向量应该朝上")
    print("✓ 平面方程测试通过")
    
    // 测试4: 坡度分析器
    let analyzer = SlopeAnalyzer()
    var testPoints: [simd_float3] = []
    for i in 0..<10 {
        let x = Float(i) * 0.1
        let z = Float(i) * 0.1
        let y = 0.1 * x + 0.05 * z
        testPoints.append(simd_float3(x, y, z))
    }
    
    if let fittedPlane = analyzer.fitPlane(points: testPoints) {
        assert(abs(fittedPlane.a - 0.1) < 0.1, "平面拟合a系数误差过大")
        print("✓ 坡度分析器测试通过")
    } else {
        print("❌ 坡度分析器测试失败")
    }
    
    // 测试5: 走廊ROI选择器
    let roi = CorridorROISelector()
    let ball = simd_float3(0, 0, 0)
    let hole = simd_float3(2, 0, 0)
    var pointCloud: [simd_float3] = []
    
    // 创建测试点云
    for x in stride(from: -1.0, through: 3.0, by: 0.1) {
        for z in stride(from: -1.0, through: 1.0, by: 0.1) {
            pointCloud.append(simd_float3(Float(x), 0, Float(z)))
        }
    }
    
    let corridorPoints = roi.selectCorridor(pointCloud: pointCloud, ball: ball, hole: hole)
    assert(corridorPoints.count > 0, "走廊点选择失败")
    print("✓ 走廊ROI选择器测试通过")
    
    // 测试6: 物理模拟器
    let physics = PhysicsSimulator()
    let flatPlane = PlaneEquation(
        a: 0, b: 0, c: 0,
        normal: simd_float3(0, 1, 0),
        slopeAngle: 0,
        downhillDirection: simd_float3(1, 0, 0)
    )
    
    let trajectory = physics.simulate(
        initialPosition: simd_float3(0, 0, 0),
        launchAngle: 0,
        launchSpeed: 1.0,
        plane: flatPlane
    )
    
    assert(trajectory.count > 5, "轨迹点数太少")
    assert(trajectory.first!.x == 0, "起始点错误")
    print("✓ 物理模拟器测试通过")
    
    // 测试7: 轨迹优化器
    let optimizer = TrajectoryOptimizer()
    let speed = optimizer.estimateBaseSpeed(distance: 3.0)
    assert(speed > 0, "速度估算错误")
    
    let angle = optimizer.findOptimalAngle(ball: ball, hole: hole, plane: flatPlane)
    assert(abs(angle) < .pi/2, "角度优化结果不合理")
    print("✓ 轨迹优化器测试通过")
    
    print("🎉 所有快速测试通过！核心算法工作正常。")
}

// 如果直接运行此文件
if CommandLine.arguments.contains("--test") {
    runQuickTests()
}
