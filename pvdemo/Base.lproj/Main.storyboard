<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--ARView Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController storyboardIdentifier="ARViewController" id="BYZ-38-t0r" customClass="ARViewController" customModule="pvdemo" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <arscnView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BrB-h1-WRS">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                            </arscnView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="点击标记球位置" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lbX-gl-BK7">
                                <rect key="frame" x="20" y="79" width="353" height="24"/>
                                <fontDescription key="fontDescription" type="system" pointSize="20"/>
                                <color key="textColor" systemColor="systemBackgroundColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="BrB-h1-WRS" firstAttribute="top" secondItem="8bC-Xf-vdC" secondAttribute="top" id="0fL-6e-Vrt"/>
                            <constraint firstItem="BrB-h1-WRS" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" id="6V7-25-0aR"/>
                            <constraint firstAttribute="bottom" secondItem="BrB-h1-WRS" secondAttribute="bottom" id="6h7-MZ-EuO"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="BrB-h1-WRS" secondAttribute="trailing" id="7ZT-J3-dGP"/>
                            <constraint firstItem="lbX-gl-BK7" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="20" id="Bqo-3N-cQs"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="lbX-gl-BK7" secondAttribute="trailing" constant="20" id="Dgh-Nt-8HZ"/>
                            <constraint firstItem="lbX-gl-BK7" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" constant="20" id="eqD-2P-2Sy"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="arView" destination="BrB-h1-WRS" id="Gfx-fD-5Ef"/>
                        <outlet property="instructionLabel" destination="lbX-gl-BK7" id="Hs9-Lm-0aG"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzz" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="20.610687022900763" y="3.5211267605633805"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
