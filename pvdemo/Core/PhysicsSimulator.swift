//
//  PhysicsSimulator.swift
//  pvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/4.
//

import Foundation
import simd

public final class PhysicsSimulator {
    public init() {}

    public func simulate(initialPosition: simd_float3, 
                        launchAngle: Float, 
                        launchSpeed: Float, 
                        plane: PlaneEquation) -> [simd_float3] {
        var pts: [simd_float3] = []
        pts.reserveCapacity(1024)
        
        let dt = PhysicsConstants.timeStep
        var p = initialPosition
        var v = launchSpeed * simd_float3(cos(launchAngle), 0, sin(launchAngle))
        var t: Float = 0
        
        let up = plane.normal // normalized
        let gVec = simd_float3(0, -PhysicsConstants.gravity, 0)
        
        while t < PhysicsConstants.maxSimTime && simd_length(v) > PhysicsConstants.minVelocity {
            // gravity tangential component (project gravity onto plane)
            let gParallel = gVec - simd_dot(gVec, up) * up
            var a = gParallel
            
            if simd_length(v) > 1e-6 { 
                a += (-PhysicsConstants.rollingFriction) * simd_normalize(v) 
            }
            
            v += a * dt
            p += v * dt
            
            // constrain y to plane
            p.y = plane.a * p.x + plane.b * p.z + plane.c
            
            // subsample for rendering (every 2 steps)
            if pts.isEmpty || (pts.count % 2 == 0) { 
                pts.append(p) 
            }
            
            t += dt
        }
        
        if pts.isEmpty || pts.last! != p { 
            pts.append(p) 
        }
        
        return pts
    }

    public func checkHoleHit(trajectory: [simd_float3], 
                           hole: simd_float3, 
                           dt: Float = PhysicsConstants.timeStep) -> Bool {
        guard trajectory.count >= 2 else { return false }
        
        for i in 1..<trajectory.count {
            let p = trajectory[i]
            let q = trajectory[i-1]
            let v = simd_length(p - q) / dt
            let d = distanceXZ(p, hole)
            
            if d <= PhysicsConstants.holeRadius && v <= PhysicsConstants.hitVelocity { 
                return true 
            }
        }
        
        return false
    }
}
