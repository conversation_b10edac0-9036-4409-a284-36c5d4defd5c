//
//  ContentView.swift
//  pvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/4.
//

import SwiftUI

struct ContentView: View {
    var body: some View {
        ARViewControllerWrapper()
            .ignoresSafeArea()
    }
}

struct ARViewControllerWrapper: UIViewControllerRepresentable {
    func makeUIViewController(context: Context) -> ARViewController {
        let storyboard = UIStoryboard(name: "Main", bundle: nil)
        return storyboard.instantiateViewController(withIdentifier: "ARViewController") as! ARViewController
    }
    
    func updateUIViewController(_ uiViewController: ARViewController, context: Context) {
        // No updates needed
    }
}

#Preview {
    ContentView()
}
