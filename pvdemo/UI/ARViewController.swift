//
//  ARViewController.swift
//  pvdemo
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/9/4.
//

import UIKit
import ARKit
import SceneKit

final class ARViewController: UIViewController, ARSCNViewDelegate {
    @IBOutlet weak var arView: ARSCNView!
    @IBOutlet weak var instructionLabel: UILabel!

    private let lidar = LiDARManager()
    private let roi = CorridorROISelector()
    private let analyzer = SlopeAnalyzer()
    private let smoother = ParameterSmoother(alpha: 0.25)
    private let optimizer = TrajectoryOptimizer()
    private let physics = PhysicsSimulator()
    private let lineRenderer = LineRenderer()

    private var ballAnchor: ARAnchor?
    private var holeAnchor: ARAnchor?
    private var trajNode: SCNNode?

    override func viewDidLoad() {
        super.viewDidLoad()
        setupARView()
        setupGestures()
        updateInstructions()

        // 运行基础算法测试
        AlgorithmTests.runBasicTests()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        lidar.configure(session: arView.session)
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        arView.session.pause()
    }
    
    private func setupARView() {
        arView.delegate = self
        arView.scene = SCNScene()
        arView.autoenablesDefaultLighting = true
        arView.automaticallyUpdatesLighting = true
    }
    
    private func setupGestures() {
        let tap = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        arView.addGestureRecognizer(tap)
    }
    
    private func updateInstructions() {
        if ballAnchor == nil {
            instructionLabel.text = "点击标记球位置"
        } else if holeAnchor == nil {
            instructionLabel.text = "点击标记球洞位置"
        } else {
            instructionLabel.text = "轨迹已生成（再次点击重置）"
        }
    }

    @objc private func handleTap(_ g: UITapGestureRecognizer) {
        let pt = g.location(in: arView)
        let results = arView.raycast(from: pt, allowing: .estimatedPlane, alignment: .any)
        
        guard let r = results.first else { 
            showAlert(message: "请点击检测到的平面上")
            return 
        }
        
        let T = r.worldTransform
        
        if ballAnchor == nil {
            ballAnchor = ARAnchor(transform: T)
            arView.session.add(anchor: ballAnchor!)
            updateInstructions()
        } else if holeAnchor == nil {
            holeAnchor = ARAnchor(transform: T)
            arView.session.add(anchor: holeAnchor!)
            updateInstructions()
            computeAndRender()
        } else {
            resetAll()
        }
    }

    private func computeAndRender() {
        guard let ballT = ballAnchor?.transform, 
              let holeT = holeAnchor?.transform else { return }
        
        let ball = simd_make_float3(ballT.columns.3)
        let hole = simd_make_float3(holeT.columns.3)
        
        // Build/limit point cloud
        let cloud = lidar.currentPointCloud
        let corridor = roi.selectCorridor(pointCloud: cloud, ball: ball, hole: hole, width: 0.45)
        
        guard corridor.count >= 20 else { 
            showAlert(message: "点云不足，请缓慢扫过球与洞路径")
            return 
        }
        
        // Rough fit then tighten by distance-to-plane (<= 8mm) and refit
        guard let rough = analyzer.fitPlane(points: corridor) else { 
            showAlert(message: "平面拟合失败")
            return 
        }
        
        let tight = corridor.filter { p in 
            abs(p.y - (rough.a*p.x + rough.b*p.z + rough.c)) < 0.008 
        }
        
        guard let plane0 = analyzer.fitPlane(points: tight.isEmpty ? corridor : tight) else { 
            showAlert(message: "平面拟合失败")
            return 
        }
        
        let plane = smoother.smooth(plane0)
        
        // Optimize angle & simulate
        let angle = optimizer.findOptimalAngle(ball: ball, hole: hole, plane: plane)
        let v0 = optimizer.estimateBaseSpeed(distance: simd_length(hole - ball))
        let traj = physics.simulate(initialPosition: ball, 
                                  launchAngle: angle, 
                                  launchSpeed: v0, 
                                  plane: plane)
        
        // Render
        trajNode?.removeFromParentNode()
        trajNode = lineRenderer.makeLineNode(points: traj)
        arView.scene.rootNode.addChildNode(trajNode!)
        
        updateInstructions()
    }

    private func resetAll() {
        if let a = ballAnchor { arView.session.remove(anchor: a) }
        if let a = holeAnchor { arView.session.remove(anchor: a) }
        ballAnchor = nil
        holeAnchor = nil
        trajNode?.removeFromParentNode()
        trajNode = nil
        smoother.reset()
        updateInstructions()
    }
    
    private func showAlert(message: String) {
        let alert = UIAlertController(title: "提示", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    // MARK: - ARSCNViewDelegate
    
    func renderer(_ renderer: SCNSceneRenderer, nodeFor anchor: ARAnchor) -> SCNNode? {
        let node = SCNNode()
        
        if anchor == ballAnchor {
            // 创建球的可视化
            let sphere = SCNSphere(radius: 0.02)
            let material = SCNMaterial()
            material.diffuse.contents = UIColor.white
            sphere.materials = [material]
            node.geometry = sphere
        } else if anchor == holeAnchor {
            // 创建洞的可视化
            let cylinder = SCNCylinder(radius: 0.054, height: 0.01)
            let material = SCNMaterial()
            material.diffuse.contents = UIColor.black
            cylinder.materials = [material]
            node.geometry = cylinder
        }
        
        return node
    }
}
