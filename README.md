# PuttVision Demo - iOS高尔夫推杆AR应用

基于LiDAR的高精度推杆轨迹AR应用，实现实时扫描 → 锚定球洞 → 计算弯曲轨迹线的完整功能。

## 🎯 项目特性

- **LiDAR优先策略**: 支持iPhone 12 Pro+的LiDAR设备，提供高精度点云数据
- **智能平面拟合**: 走廊ROI采样 + 二次拟合，精确分析果岭坡度
- **物理模拟**: 基于显式积分的真实物理模拟，考虑重力和滚动摩擦
- **角度优化**: 黄金分割搜索算法，找到最优推杆角度
- **实时渲染**: SceneKit线段几何渲染，流畅显示轨迹

## 📁 项目结构

```
PuttVisionDemo/
├── Core/                           # 核心算法模块
│   ├── PlaneEquation.swift         # 平面方程数据结构
│   ├── SlopeAnalyzer.swift         # 平面拟合和坡度计算
│   ├── CorridorROISelector.swift   # 走廊ROI采样
│   ├── PhysicsSimulator.swift      # 显式积分物理模拟
│   ├── TrajectoryOptimizer.swift   # 角度搜索优化
│   └── LiDARManager.swift          # LiDAR数据获取
├── Rendering/
│   └── LineRenderer.swift          # 线段几何渲染
├── Interaction/
│   └── ParameterSmoother.swift     # 参数平滑
├── Utils/
│   ├── MathExtensions.swift        # 数学工具扩展
│   └── PhysicsConstants.swift      # 物理常数
├── UI/
│   └── ARViewController.swift      # 主AR控制器
├── Tests/
│   ├── AlgorithmTests.swift        # 算法验证测试
│   └── QuickTest.swift            # 快速测试
└── Base.lproj/
    └── Main.storyboard            # 界面布局
```

## 🚀 快速开始

### 系统要求
- **iOS版本**: 15.4+
- **推荐设备**: iPhone 12 Pro/Max, 13 Pro/Max, 14 Pro/Max (LiDAR)
- **降级支持**: 非LiDAR设备显示直线轨迹

### 安装步骤

1. **打开项目**
   ```bash
   open pvdemo.xcodeproj
   ```

2. **配置开发者账号**
   - 在Xcode中选择你的开发团队
   - 确保Bundle Identifier唯一

3. **运行项目**
   - 选择真机设备（AR需要真机）
   - 点击运行按钮

### 使用方法

1. **启动应用**: 允许相机权限
2. **扫描环境**: 缓慢移动设备扫描推杆路径
3. **标记球位**: 点击屏幕标记高尔夫球位置
4. **标记洞位**: 点击屏幕标记球洞位置
5. **查看轨迹**: 应用自动计算并显示最优推杆轨迹

## 🔧 核心算法

### 1. LiDAR数据获取
- 优先使用ARMesh（LiDAR设备）
- 降级使用深度图转点云
- 稀疏采样优化性能

### 2. 走廊ROI选择
```swift
// 条带采样 + 二次拟合
let corridorPoints = roi.selectCorridor(
    pointCloud: cloud, 
    ball: ballPosition, 
    hole: holePosition, 
    width: 0.45
)
```

### 3. 平面拟合
```swift
// 正规方程求解 y = ax + bz + c
let plane = analyzer.fitPlane(points: corridorPoints)
```

### 4. 物理模拟
```swift
// 显式欧拉积分 + 重力投影
let trajectory = physics.simulate(
    initialPosition: ball,
    launchAngle: angle,
    launchSpeed: speed,
    plane: plane
)
```

### 5. 角度优化
```swift
// 黄金分割搜索最优角度
let optimalAngle = optimizer.findOptimalAngle(
    ball: ball, 
    hole: hole, 
    plane: plane
)
```

## ⚙️ 参数调优

### 物理常数 (PhysicsConstants.swift)
```swift
static var rollingFriction: Float = 0.9    // 滚动摩擦系数
static let holeRadius: Float = 0.054       // 洞半径(m)
static let hitVelocity: Float = 0.8        // 进洞最大速度
static let timeStep: Float = 0.003         // 时间步长
```

### 走廊参数
```swift
let corridorWidth: Float = 0.45  // 走廊宽度(m)
let distanceThreshold: Float = 0.008  // 平面距离阈值(8mm)
```

### 平滑参数
```swift
let alpha: Float = 0.25  // EMA平滑系数
```

## 🐛 调试功能

### 算法测试
```swift
// 在ARViewController中自动运行
AlgorithmTests.runBasicTests()
```

### 控制台输出
- 点云数量统计
- 平面拟合参数
- 轨迹优化结果
- 性能指标

## 📊 性能优化

1. **点云稀疏采样**: 每3个像素采样一次
2. **走廊ROI限制**: 只处理相关区域点云
3. **轨迹渲染优化**: 最多120个点，线段几何
4. **参数平滑**: EMA滤波减少抖动

## 🔍 故障排除

### 常见问题

1. **"点云不足"错误**
   - 解决: 缓慢移动设备扫描球到洞的完整路径
   - 确保光照充足

2. **"平面拟合失败"错误**
   - 解决: 确保推杆路径相对平坦
   - 避免复杂地形

3. **轨迹显示不准确**
   - 调整滚动摩擦系数
   - 检查球洞位置标记是否准确

### 调试技巧

1. **查看控制台日志**: 了解算法执行状态
2. **调整物理参数**: 根据实际果岭条件
3. **重置平滑器**: 环境变化时调用`smoother.reset()`

## 🚧 已知限制

1. **设备要求**: LiDAR设备效果最佳
2. **环境要求**: 需要良好光照条件
3. **地形限制**: 适用于相对平坦的推杆区域
4. **距离限制**: 推荐3-8米推杆距离

## 📈 未来改进

1. **机器学习优化**: 基于历史数据优化参数
2. **多地形支持**: 支持更复杂的果岭地形
3. **风力模拟**: 考虑环境风力影响
4. **社交功能**: 分享和比较推杆轨迹

## 📄 许可证

MIT License - 详见LICENSE文件

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

---

**注意**: 此应用仅供娱乐和学习用途，实际推杆效果可能因多种因素而异。
